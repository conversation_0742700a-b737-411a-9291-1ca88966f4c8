import os
import glob
import json
import argparse
from pathlib import Path
from collections import defaultdict

import cv2
import numpy as np
from tqdm import tqdm

from utils.utils import CameraCalibrator
from utils.face_templates import face_mesh_template_3D_MEDIAPIPE_mm, face_mesh_template_3D_MEDIAPIPE_mm_11p


HD = (1280, 720)
FHD = (1920, 1080)


def load_camera_params(cam_params):
    # Load camera poses
    cam_poses = cv2.FileStorage(cam_params, cv2.FILE_STORAGE_READ)
    num_cameras = int(cam_poses.getNode("nb_camera").real())

    # Display cameras
    camera_params = {}
    for cam_idx in range(num_cameras):
        # Get camera pose
        name = f"camera_{cam_idx}"
        K = cam_poses.getNode(name).getNode("camera_matrix").mat()
        D = cam_poses.getNode(name).getNode("distortion_vector").mat()
        cam_pose = cam_poses.getNode(name).getNode("camera_pose_matrix").mat()

        # Get distortion type (0=pinhole, 1=fisheye)
        distortion_type_node = cam_poses.getNode(name).getNode("distortion_type")
        distortion_type = int(distortion_type_node.real()) if not distortion_type_node.empty() else 1

        # Get rotation and translation
        cam_R = cam_pose[0:3, 0:3]
        cam_tvec = np.asarray([cam_pose[0:3, 3]]).T

        # Store info
        camera_params[name] = {
            "K": K,
            "D": D,
            "pose": cam_pose,
            "R": cam_R,
            "tvec": cam_tvec,
            "distortion_type": distortion_type
        }
    return camera_params


def match_images_and_keypoints(image_paths, jsons_paths):
    """ Match images and keypoints from jsons """
    scene_groups = defaultdict(lambda: defaultdict(lambda: {'image_path': None, 'keypoints': None}))

    for img_path in image_paths:
        base_name = os.path.basename(img_path)
        cam_name = img_path.split("/")[-2]
        scene_groups[base_name][cam_name]['image_path'] = img_path
    
    # Load and organize keypoints for each image
    for json_path in jsons_paths:
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        cam_name = json_path.split("/")[-2]
        # Create mappings
        image_id_to_filename = {img["id"]: img["file_name"] for img in data["images"]}
        image_id_to_keypoints = {ann["image_id"]: ann["keypoints"] for ann in data["annotations"]}

        # Add keypoints to corresponding image groups
        for img_id, filename in image_id_to_filename.items():
            if img_id in image_id_to_keypoints:
                base_name = os.path.basename(filename)
                
                if base_name in scene_groups:
                    keypoints = np.array(image_id_to_keypoints[img_id]).reshape(-1, 3)
                    keypoints = np.round(keypoints).astype(np.int32)
                    scene_groups[base_name][cam_name]['keypoints'] = keypoints
    
    return scene_groups


def get_bbox(original, keypoints, pad=200):
    """Get bounding box around keypoints with padding."""
    h, w = original.shape[:2]

    # Handle case where keypoints might be 2D projected points
    if keypoints.ndim == 2 and keypoints.shape[1] >= 2:
        if keypoints.shape[1] == 3:
            # Use only visible keypoints if visibility info is available
            visible_mask = keypoints[:, 2] == 2
            if np.any(visible_mask):
                points = keypoints[visible_mask, :2]
            else:
                points = keypoints[:, :2]
        else:
            points = keypoints[:, :2]
    else:
        points = keypoints

    if len(points) == 0:
        # Return full image if no points
        return 0, w, 0, h

    # Get center of points
    cx = int(np.mean(points[:, 0]))
    cy = int(np.mean(points[:, 1]))

    # Ensure bounding box is within image bounds
    x0 = max(cx - pad, 0)
    y0 = max(cy - pad, 0)
    x1 = min(cx + pad, w)
    y1 = min(cy + pad, h)

    # Ensure we have a valid bounding box
    if x1 <= x0 or y1 <= y0:
        return 0, w, 0, h

    return x0, x1, y0, y1


def find_origin_camera_with_most_visible_keypoints(camera_groups):
    """
    Find the camera with the most visible keypoints to use as origin.

    Args:
        camera_groups: Dictionary of camera_name -> sample_group

    Returns:
        origin_cam_name: Name of camera with most visible keypoints
        max_visible_count: Number of visible keypoints in origin camera
    """
    max_visible_count = 0
    origin_cam_name = None

    for cam_name, sample_group in camera_groups.items():
        if sample_group['keypoints'] is not None:
            # Count visible keypoints (z == 2)
            visible_count = np.sum(sample_group['keypoints'][:, 2] == 2)

            if visible_count > max_visible_count:
                max_visible_count = visible_count
                origin_cam_name = cam_name

    return origin_cam_name, max_visible_count


def get_camera_param_name(cam_name):
    """Map camera folder names to camera parameter names."""
    cam_mapping = {
        'cam_0': 'camera_0',
        'cam_2': 'camera_1',
        'cam_4': 'camera_2',
        'cam_6': 'camera_3',
        'rs_146222252273': 'camera_4',
        'rs_146222251797': 'camera_5'
    }
    return cam_mapping.get(cam_name, cam_name)


def get_face_landmarks_in_ccs_from_solvePnP(model_points, image_points, K, D):
    """ Solve PnP to get rotation and translation vectors """
    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        model_points,
        image_points,
        K,
        D,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )   # Initial fit
    
    if inliers is not None:
        second_fit_model_points = model_points[inliers[:, 0]]
        second_fit_image_points = image_points[inliers[:, 0]]
    else:
        second_fit_model_points = model_points
        second_fit_image_points = image_points
    
    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(
            second_fit_model_points,
            second_fit_image_points,
            K,
            D,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )   # Second fit for higher accuracy
    
    return rvec, tvec   # Shape: (3, N)


def transform_pose_from_origin_to_target(origin_sample, target_sample, camera_params, origin_cam_name, target_cam_name):
    """
    Transform pose from origin camera (with most visible keypoints) to target camera.

    Args:
        origin_sample: Sample group from origin camera
        target_sample: Sample group from target camera
        camera_params: Camera calibration parameters
        origin_cam_name: Name of origin camera
        target_cam_name: Name of target camera

    Returns:
        target_image: Target image with projected pose
        target_keypoints: Target keypoints
        projected_points: 2D projected points in target image
        pose_info: Dictionary with pose estimation info
    """
    # Get image paths
    origin_image_path = origin_sample['image_path']
    target_image_path = target_sample['image_path']
    
    # Get images
    origin_image = cv2.imread(origin_image_path)
    target_image = cv2.imread(target_image_path)

    # Get keypoints
    origin_keypoints = origin_sample['keypoints'].copy()
    target_keypoints = target_sample['keypoints'].copy()

    # Get camera parameter names
    origin_param_name = get_camera_param_name(origin_cam_name)
    target_param_name = get_camera_param_name(target_cam_name)

    # Get camera parameters
    origin_params = camera_params[origin_param_name]
    target_params = camera_params[target_param_name]

    # Check if cameras have distortion type info, default to fisheye (1) if not present
    origin_distortion_type = origin_params.get("distortion_type", 1)
    target_distortion_type = target_params.get("distortion_type", 1)

    # Undistort origin camera
    origin_calibrator = CameraCalibrator(origin_params["K"], origin_params["D"],
                                       is_fisheye=(origin_distortion_type == 1))
    origin_image = origin_calibrator.undistort_image(origin_image, 0.0)[0]
    origin_keypoints = origin_calibrator.undistort_point(origin_keypoints)
    K_origin, D_origin = origin_calibrator.camera_matrix_undistorted, origin_calibrator.dist_coeffs_undistorted

    # Undistort target camera
    target_calibrator = CameraCalibrator(target_params["K"], target_params["D"],
                                       is_fisheye=(target_distortion_type == 1))
    target_image = target_calibrator.undistort_image(target_image, 0.0)[0]
    target_keypoints = target_calibrator.undistort_point(target_keypoints)
    K_target, D_target = target_calibrator.camera_matrix_undistorted, target_calibrator.dist_coeffs_undistorted

    # Get visible keypoints from origin camera for pose estimation
    visible_ids = np.where(origin_keypoints[:, 2] == 2)[0]
    image_points = origin_keypoints[visible_ids, :2].astype(np.float32)

    # Get corresponding 3D model points
    model_points = face_mesh_template_3D_MEDIAPIPE_mm_11p
    model_points = model_points[visible_ids, :3]

    # Solve PnP on origin camera to get pose
    rvec_origin, tvec_origin = get_face_landmarks_in_ccs_from_solvePnP(
        model_points, image_points, K_origin, D_origin
    )

    # Draw coordinate axes on origin image
    cv2.drawFrameAxes(origin_image, K_origin, D_origin, rvec_origin, tvec_origin, 50, thickness=3)

    # Project points back to origin image for verification
    proj_points_origin = cv2.projectPoints(
        model_points, rvec_origin, tvec_origin, K_origin, D_origin
    )[0].reshape(-1, 2)

    # Draw projected points on origin
    for p in proj_points_origin:
        cv2.circle(origin_image, (int(p[0]), int(p[1])), 2, (255, 255, 255), -1)

    # Transform pose from origin camera to target camera using extrinsics
    rvec_target, tvec_target = origin_calibrator.transform_object_pose_between_cameras(
        rvec_obj_in_src_cam=rvec_origin,
        tvec_obj_in_src_cam=tvec_origin,
        src_cam_extrinsic=origin_params["pose"],
        dst_cam_extrinsic=target_params["pose"],
        verbose=False,
    )

    # Draw coordinate axes on target image
    cv2.drawFrameAxes(target_image, K_target, D_target, rvec_target, tvec_target, 50, thickness=3)

    # Project all model points to target camera
    templates = [face_mesh_template_3D_MEDIAPIPE_mm, face_mesh_template_3D_MEDIAPIPE_mm_11p]
    
    proj_points_target = cv2.projectPoints(
        templates[1], rvec_target, tvec_target, K_target, D_target
    )[0].reshape(-1, 2)

    # Draw projected points on target
    for p in proj_points_target:
        cv2.circle(target_image, (int(p[0]), int(p[1])), 2, (255, 255, 255), -1)

    pose_info = {
        'origin_cam_name': origin_cam_name,
        'target_cam_name': target_cam_name,
        'visible_keypoints_count': len(visible_ids),
        'origin_image_path': origin_image_path,
        'target_image_path': target_image_path,
        'origin_image': origin_image,
        'target_image': target_image,
        'rvec_origin': rvec_origin,
        'tvec_origin': tvec_origin,
        'rvec_target': rvec_target,
        'tvec_target': tvec_target,
        'origin_keypoints': origin_keypoints,
        'target_keypoints': target_keypoints,
        'proj_points_target': proj_points_target
    }

    return pose_info


def process_all_cameras_from_origin(camera_groups, camera_params, origin_cam_name):
    """
    Process all cameras using the origin camera with most visible keypoints.

    Args:
        camera_groups: Dictionary of camera_name -> sample_group
        camera_params: Camera calibration parameters
        origin_cam_name: Name of origin camera with most visible keypoints

    Returns:
        results: Dictionary with processed results for each target camera
    """
    results = {}
    origin_sample = camera_groups[origin_cam_name]

    for target_cam_name, target_sample in camera_groups.items():
        if target_cam_name == origin_cam_name:
            continue

        if target_sample['keypoints'] is None or target_sample['image_path'] is None:
            continue

        try:
            pose_info = transform_pose_from_origin_to_target(
                origin_sample,
                target_sample,
                camera_params,
                origin_cam_name,
                target_cam_name
            )
            results[target_cam_name] = pose_info

        except Exception as e:
            print(f"Error processing {target_cam_name}: {str(e)}")
            continue

    return results


def display_images(args):
    max_points = '11'
    freeze = args.freeze
    pad = 200
    size = 600
    size_x, size_y = 400, 250
    point_size = 2
    
    winname_0 = "Origin Camera - Pose Estimation"
    winname_1 = "Target Camera - All Points"
    winname_2 = "Target Camera - Only Visible Points"
    
    if args.show_frame:
        for idx, winname in enumerate([winname_0, winname_1, winname_2]):
            cv2.namedWindow(winname, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(winname, width=size, height=size)
            cv2.moveWindow(winname, x=(size_x+pad) * idx, y=size_y)
    
    # Load camera params
    camera_params = load_camera_params(args.cam_params)
    
    # All images in the folder
    image_paths = sorted(
        glob.glob(os.path.join(args.images, "**", "*.png"), recursive=True)
    )

    # All jsons in the folder
    jsons_paths = sorted(
        glob.glob(os.path.join(args.jsons, "**", "*.json"), recursive=True)
    )

    # Leave only specific class filter
    image_paths = [name for name in image_paths if args.filter in name]

    # Leave only specific class filter
    jsons_paths = [name for name in jsons_paths if args.filter in name]

    # Match images and keypoints
    scene_groups = match_images_and_keypoints(image_paths, jsons_paths)
    
    # Iterate through the images
    for image_name, camera_groups in tqdm(scene_groups.items()):
        # Find the camera with the most visible keypoints to use as origin
        origin_cam_name, max_visible_count = find_origin_camera_with_most_visible_keypoints(camera_groups)

        # Skip if no origin camera found or not enough visible keypoints
        if origin_cam_name is None or max_visible_count < 5:
            print(f"Skipping {image_name}: Not enough visible keypoints found: {max_visible_count}")
            continue

        print(f"[INFO] Visible keypoints: {max_visible_count:0{len(max_points)}d}/{max_points} | Origin camera: {origin_cam_name}")

        # Process all cameras from origin
        all_cameras_pose_info = process_all_cameras_from_origin(camera_groups, camera_params, origin_cam_name)

        # Display results
        for target_cam_name, pose_info in all_cameras_pose_info.items():
            # Get the origin image for display
            origin_image = pose_info['origin_image']
            target_image = pose_info['target_image']
            origin_keypoints = pose_info['origin_keypoints']
            target_keypoints = pose_info['target_keypoints']
            proj_points = pose_info['proj_points_target']

            # Create visible keypoints overlay for target
            target_image_visible = target_image.copy()

            # Draw keypoints on target image
            for x, y, z in origin_keypoints:
                if z == 1:      # invisible
                    cv2.circle(origin_image,          (x, y), point_size, (0, 0, 255), -1, cv2.LINE_AA)
                elif z == 2:    # visible
                    cv2.circle(origin_image,          (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)

            # Draw keypoints on target image
            for x, y, z in target_keypoints:
                if z == 1:      # invisible
                    cv2.circle(target_image,          (x, y), point_size, (0, 0, 255), -1, cv2.LINE_AA)
                elif z == 2:    # visible
                    cv2.circle(target_image,          (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)
                    cv2.circle(target_image_visible,  (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)

            # Get face bounding boxes for cropping
            x0, x1, y0, y1 = get_bbox(origin_image, origin_keypoints, pad)
            origin_image_cropped = origin_image[y0:y1, x0:x1]
            
            x0, x1, y0, y1 = get_bbox(target_image, target_keypoints, pad)
            target_image_cropped = target_image[y0:y1, x0:x1]
            target_image_visible_cropped = target_image_visible[y0:y1, x0:x1]

            # Add text overlay showing camera information
            if args.show_frame:
                # Add text to images showing camera info
                origin_display = origin_image_cropped.copy()
                target_display = target_image_cropped.copy()

                cv2.putText(origin_display, f"Origin: {origin_cam_name} ({max_visible_count} pts)", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(target_display, f"Target: {target_cam_name} ({max_points} pts)", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(target_image_visible_cropped, f"Target: {target_cam_name}", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                cv2.imshow(winname_0, origin_display)
                cv2.imshow(winname_1, target_display)
                cv2.imshow(winname_2, target_image_visible_cropped)

                # Get a key
                key = cv2.waitKey(freeze) & 0xFF

                if key == 27:           # Press 'Esc' to quit
                    cv2.destroyAllWindows()
                    exit()
                elif key == ord('q'):   # Move to next image
                    break
                elif key == ord('f'):   # Toggle freeze mode
                    freeze = 0 if freeze else 1

            # Save results if requested
            if args.save:
                origin_image_path = pose_info['origin_image_path']
                target_image_path = pose_info['target_image_path']
            
                # Extract TV name from image path (assuming format: .../TV_name/camera_name/...)
                p_name = Path(origin_image_path).parts[2]   # Adjust index based on your path structure
                tv_name = Path(origin_image_path).parts[3]  # Adjust index based on your path structure
                
                # Create directory structure: visualized_results/TV_name/camera_name/
                output_base = Path(args.output_dir)
                output_dir_origin = output_base / p_name / tv_name / origin_cam_name
                output_dir_target = output_base / p_name / tv_name / target_cam_name
                output_dir_origin.mkdir(parents=True, exist_ok=True)
                output_dir_target.mkdir(parents=True, exist_ok=True)

                # Save images with descriptive names
                base_name = Path(image_name).stem

                # Check if images are valid before saving
                if origin_image_cropped.size > 0:
                    origin_output = output_dir_origin / f"{base_name}_origin.jpg"
                    cv2.imwrite(str(origin_output), origin_image_cropped)
                else:
                    print(f"Warning: Empty origin image for {image_name}")

                if target_image_cropped.size > 0:
                    target_output = output_dir_target / f"{base_name}_target.jpg"
                    cv2.imwrite(str(target_output), target_image_cropped)
                else:
                    print(f"Warning: Empty target image for {image_name}")

                # Save pose information in the same directory structure
                pose_file = output_dir_target / f"{base_name}.json"
                pose_data = {
                    'tv_name': tv_name,
                    'image_name': image_name,
                    'origin_camera': origin_cam_name,
                    'target_camera': target_cam_name,
                    'visible_keypoints_count': int(max_visible_count),
                    'origin_rvec': pose_info['rvec_origin'].tolist(),
                    'origin_tvec': pose_info['tvec_origin'].tolist(),
                    'target_rvec': pose_info['rvec_target'].tolist(),
                    'target_tvec': pose_info['tvec_target'].tolist()
                }

                with open(pose_file, 'w') as f:
                    json.dump(pose_data, f, indent=2)

    if args.show_frame:
        cv2.destroyAllWindows()


def main(root='data'):
    parser = argparse.ArgumentParser(description="Transform poses from origin camera with most visible keypoints to other cameras")
    parser.add_argument('-i', '--images', default=f'{root}/images', type=str, help="image folder path")
    parser.add_argument('-j', '--jsons', default=f'{root}/jsons', type=str, help="json file path")
    parser.add_argument('-o', '--output-dir', default=f'{root}/visualized_results_v3', type=str, help="Output directory")
    parser.add_argument('-c', '--cam-params', default='camera_params/calibrated_cameras_data_v3.yml', type=str, help="Camera parameters file")
    parser.add_argument('-f', '--filter', default='', type=str, help="Search by filter: '2024-09-04'")
    parser.add_argument('-p', '--show-frame', action='store_true', help="Show frame for verification")
    parser.add_argument('-s', '--save', action='store_true', help="Save processed images")
    parser.add_argument('--freeze', default=0, type=int, help="Freeze mode (0=continuous, 1=wait for key)")
    args = parser.parse_args()

    display_images(args)


if __name__=="__main__":
    main()
