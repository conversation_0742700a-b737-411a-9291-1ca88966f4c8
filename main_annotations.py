import os
import glob
import json
import argparse
from pathlib import Path

import cv2
import numpy as np
from tqdm import tqdm

from utils.utils import CameraCalibrator
from utils.face_templates import face_mesh_template_3D_MEDIAPIPE_μm
from utils.face_templates import head_pose_indices_deltax


HD = (1280, 720)
FHD = (1920, 1080)

def project_point(point3d, K):
    X, Y, Z = point3d
    u = (K[0, 0] * X + K[0, 2] * Z) / Z
    v = (K[1, 1] * Y + K[1, 2] * Z) / Z
    return np.array([u, v], dtype=float)


def draw_coordinate_axes_px(img, face_center_3d, R, K, axis_length=80, thickness=3):
    """
    Draws a 3D coordinate system (axes) on the face in the image.
    :param img: Image to draw on
    :param face_center_3d: Origin point in 3D (camera space)
    :param R: 3x3 rotation matrix of the head
    :param K: Camera intrinsic matrix
    :param axis_length: Length of axes to draw (in mm)
    """
    # Axes in 3D camera space
    axes = np.eye(3) * axis_length  # X, Y, Z axes

    # Rotate axes using head rotation matrix
    rotated_axes = R @ axes  # shape (3, 3)

    # Get endpoints in 3D
    endpoints_3d = face_center_3d.reshape(3, 1) + rotated_axes

    # Project to 2D
    origin_2d = project_point(face_center_3d, K)
    x_2d = project_point(endpoints_3d[:, 0], K)
    y_2d = project_point(endpoints_3d[:, 1], K)
    z_2d = project_point(endpoints_3d[:, 2], K)

    # Convert to int tuples
    origin_2d = tuple(map(int, origin_2d))
    x_2d = tuple(map(int, x_2d))
    y_2d = tuple(map(int, y_2d))
    z_2d = tuple(map(int, z_2d))

    # Draw each axis: X (Red), Y (Green), Z (White)
    cv2.line(img, origin_2d, z_2d, (255, 255, 255), thickness) # Z: directed inside head
    cv2.line(img, origin_2d, x_2d, (0, 0, 0), thickness)
    cv2.line(img, origin_2d, y_2d, (0, 0, 0), thickness)

    return img


def load_camera_params():
    # Load camera poses
    cam_poses = cv2.FileStorage("camera_tv_params/calibrated_cameras_data_v2.yml", cv2.FILE_STORAGE_READ)
    num_cameras = int(cam_poses.getNode("nb_camera").real())

    # Display cameras
    camera_params = {}
    for cam_idx in range(num_cameras):
        # Get camera pose
        name = f"camera_{cam_idx}"
        K = cam_poses.getNode(name).getNode("camera_matrix").mat()
        D = cam_poses.getNode(name).getNode("distortion_vector").mat()
        cam_pose = cam_poses.getNode(name).getNode("camera_pose_matrix").mat()
        
        # Get rotation and translation
        cam_R = cam_pose[0:3, 0:3]
        cam_tvec = np.asarray([cam_pose[0:3, 3]]).T
        
        # Store info
        camera_params[name] = {"K": K, "D": D, "pose": cam_pose, "R": cam_R, "tvec": cam_tvec}
    return camera_params

def transform_3d_points_between_cams(pts_3d_src, T_src, T_dst):
    """
    pts_3d_src : (N, 3) keypoints in source camera coordinates
    T_src      : (4, 4) extrinsic of source camera (world←src)
    T_dst      : (4, 4) extrinsic of destination camera (world←dst)
    """
    # make homogeneous
    pts_h = np.hstack([pts_3d_src, np.ones((len(pts_3d_src), 1))])  # (N,4)

    # transform
    T_src_to_dst = np.linalg.inv(T_dst) @ T_src
    pts_3d_dst_h = (T_src_to_dst @ pts_h.T).T  # (N,4)

    return pts_3d_dst_h[:, :3]  # (N,3)


def get_face_landmarks_in_ccs_from_solvePnP(model_points, image_points, K, D):
    """ Solve PnP to get rotation and translation vectors """
    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        model_points,
        image_points,
        K,
        D,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )   # Initial fit
    if inliers is not None:
        m = model_points[inliers[:, 0]]
        i = image_points[inliers[:, 0]]
    else:
        m = model_points
        i = image_points
    
    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(
            m,
            i,
            K,
            D,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )   # Second fit for higher accuracy
    
    # success, rvec, tvec = cv2.solvePnP(
    #     model_points,
    #     image_points,
    #     K,
    #     D,
    #     None,
    #     None,
    #     flags=cv2.SOLVEPNP_ITERATIVE
    # )
    
    R = cv2.Rodrigues(rvec)[0]
    landmarks_3D = np.dot(R, model_points.T) + tvec
    
    inliers=None
    
    return landmarks_3D.T, inliers, rvec, tvec # Shape: (3, N)


def transform(image, original_0, keypoints, camera_params):
    params = camera_params["camera_1"]
    calibrator = CameraCalibrator(params["K"], params["D"])
    image = calibrator.undistort_image(image, 0.0)[0]
    keypoints = calibrator.undistort_point(keypoints)
    K_new, D_new = calibrator.camera_matrix_undistorted, calibrator.dist_coeffs_undistorted
    
    
    ids = np.where(keypoints[:, 2] == 2)[0]   # indices where condition is true
    image_points = keypoints[ids, :2].astype(np.float32)         # the corresponding (x,y)
    
    model_points = face_mesh_template_3D_MEDIAPIPE_μm[head_pose_indices_deltax, :3]
    model_points = model_points[ids, :3]
    
    
    
    landmarks_3D, inliers, rvec, tvec = get_face_landmarks_in_ccs_from_solvePnP(
        model_points, image_points, K_new, D_new
    )
    T_landmarks_3D = transform_3d_points_between_cams(
        landmarks_3D,
        camera_params["camera_1"]["pose"],
        camera_params["camera_0"]["pose"]
    )
    
    R = cv2.Rodrigues(rvec)[0]
    draw_coordinate_axes_px(image, tvec, R, K_new, axis_length=80, thickness=3)
    # cv2.drawFrameAxes(image, K_new, D_new, rvec, tvec, 50, thickness=3)
    
    
    params = camera_params["camera_0"]
    calibrator_0 = CameraCalibrator(params["K"], params["D"])
    original_0 = calibrator_0.undistort_image(original_0, 0.0)[0]
    K_new_0, D_new_0 = calibrator.camera_matrix_undistorted, calibrator.dist_coeffs_undistorted
    
    # success, rvec, tvec = calibrator.estimate_pose_solvePnP(imgpoints, objpoints)
    # calibrator.draw_pose_cv2(image, rvec, tvec, axis_length=300)
    
    rvec_obj_in_dst_cam, tvec_obj_in_dst_cam = calibrator.transform_object_pose_between_cameras(
        rvec_obj_in_src_cam=rvec,
        tvec_obj_in_src_cam=tvec,
        src_cam_extrinsic=camera_params["camera_1"]["pose"],
        dst_cam_extrinsic=camera_params["camera_0"]["pose"],
        verbose=False,
    )
    cv2.drawFrameAxes(original_0, K_new_0, D_new_0, rvec_obj_in_dst_cam, tvec_obj_in_dst_cam, 300, thickness=3)

    proj_points, _ = cv2.projectPoints(
        T_landmarks_3D,
        rvec_obj_in_dst_cam,
        tvec_obj_in_dst_cam,
        K_new_0,
        D_new_0,
    )
    proj_points = proj_points.reshape(-1, 2)
    # Draw projected points
    for p in proj_points:
        cv2.circle(original_0, (int(p[0]), int(p[1])), 5, (0, 255, 0), -1)
    
    return image, keypoints, original_0, proj_points


def get_bbox(original, keypoints, pad=200):
    # Get center of visible koordinate
    w, h = original.shape[1], original.shape[0]
    # visible_keypoints = keypoints[keypoints[:, 2] == 2]
    visible_keypoints = keypoints
    cx = round(np.mean(visible_keypoints[:, 0]))
    cy = round(np.mean(visible_keypoints[:, 1]))

    x0 = max(cx-pad, 0)
    y0 = max(cy-pad, 0)
    x1 = min(cx+pad, w)
    y1 = min(cy+pad, h)
    return x0, x1, y0, y1


def display_images(args):
    freeze = args.freeze
    pad = 200
    size = 600
    size_x, size_y = 400, 250
    point_size = 2
    
    winname_0 = "Body Keypoints"
    winname_1 = "Body Keypoints Original"
    winname_2 = "Body Keypoints Original Extended"
    
    if args.show_frame:
        for idx, winname in enumerate([winname_0, winname_1, winname_2]):
            cv2.namedWindow(winname, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(winname, width=size, height=size)
            cv2.moveWindow(winname, x=(size_x+pad) * idx, y=size_y)
    
    # Load camera params
    camera_params = load_camera_params()
    
    # All images in the folder
    image_paths = sorted(
        glob.glob(os.path.join(args.images, "**", "*.png"), recursive=True)
    )

    # All jsons in the folder
    jsons_paths = sorted(
        glob.glob(os.path.join(args.jsons, "**", "*.json"), recursive=True)
    )

    # Leave only specific class filter
    image_paths = [name for name in image_paths if args.filter in name]

    # Leave only specific class filter
    jsons_paths = [name for name in jsons_paths if args.filter in name]

    # Iterate through the images
    for image_path in tqdm(image_paths):
        image_path_0 = image_path.replace("cam_2", "cam_0")

        parts = image_path.split("/")[2:5]
        merged = "/".join(parts)
        output_dir = f"{args.output_dir}/{parts[0]}/{parts[1]}/{parts[2]}"

        # Load annotations
        json_path_found = None
        for json_path in jsons_paths:
            if merged in json_path:
                json_path_found = json_path
            else:
                continue
        
        if json_path_found is None:
            print(f"Json not found for {image_path}")
            continue
        
        with open(json_path_found, 'r') as f:
            data = json.load(f)
        
        # Create a dictionary mapping image IDs to filenames
        image_id_to_filename = {img["id"]: img["file_name"] for img in data["images"]}
        
        # Create a dictionary mapping image IDs to keypoints
        image_id_to_keypoints = {ann["image_id"]: ann["keypoints"] for ann in data["annotations"]}
        
        # Create a dictionary mapping filenames to keypoints
        filename_to_keypoints = {
            image_id_to_filename[img_id]: keypoints 
            for img_id, keypoints in image_id_to_keypoints.items()
        }
    
        # Read image 
        original = cv2.imread(image_path)
        original_0 = cv2.imread(image_path_0)
        
        # Find image info by filename
        image_name = os.path.basename(image_path)
        keypoints = filename_to_keypoints[image_name]
        keypoints = np.array(keypoints).reshape(-1, 3)
        keypoints = np.round(keypoints).astype(np.int32)

        original, keypoints, original_0, proj_points = transform(original, original_0, keypoints, camera_params)
        image = original.copy()

        for i, (x, y, z) in enumerate(keypoints):
            
            if z == 1:      # invisible
                cv2.circle(original, (x, y), point_size, (0, 0, 255), -1, cv2.LINE_AA)
            elif z == 2:    # visible
                cv2.circle(image, (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)
                cv2.circle(original, (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)

        # Get face
        x0, x1, y0, y1 = get_bbox(original, keypoints, pad)
        face_image_original = original[y0:y1, x0:x1]
        face_image_vissualized = image[y0:y1, x0:x1]
        
        # x0, x1, y0, y1 = get_bbox(original_0, proj_points, pad)
        # original_0_cropeed = original_0[y0:y1, x0:x1]
        # original_0_cropeed = original_0

        # Show results
        if args.show_frame:
            cv2.imshow(winname_0, face_image_vissualized)
            cv2.imshow(winname_1, face_image_original)
            # cv2.imshow(winname_2, original_0_cropeed)
            
            # Get a key
            key = cv2.waitKey(freeze) & 0xFF

            if key == ord('q'):     # Press 'q' to quit
                break
            elif key == ord('f'):   # Toggle freeze mode
                freeze = 0 if freeze else 1

        # Save frames
        if args.save:
            Path(output_dir).mkdir(parents=True, exist_ok=True)

            # Resize both images
            face_image_original = cv2.resize(face_image_original, (600, 600))
            face_image_vissualized = cv2.resize(face_image_vissualized, (600, 600))
            
            # Concatenate images horizontally
            merged_image = np.hstack((face_image_original, face_image_vissualized))
            
            # Save merged image
            file_name = f"{output_dir}/{image_name}.png"
            cv2.imwrite(file_name, merged_image)

    if args.show_frame:
        cv2.destroyAllWindows()


def main(root='data'):
    parser = argparse.ArgumentParser()
    parser.add_argument('-j', '--jsons', default=f'{root}/jsons', type=str, help="json file path")
    parser.add_argument('-i', '--images', default=f'{root}/images', type=str, help="image folder path")
    parser.add_argument('-f', '--filter', default='TV_2/cam_2', type=str, help="Search by filter: '2024-09-04'")
    parser.add_argument('-o', '--output-dir', default=f'{root}/visualized_results', type=str, help="Output directory")
    parser.add_argument('-sf', '--show-frame', action='store_true', help="Show frame for verification")
    parser.add_argument('-s', '--save', action='store_true', help="Show 'src' and 'dst' paths for verification")
    parser.add_argument('--freeze', default=0, type=int, help="Freeze mode")
    args = parser.parse_args()
    
    display_images(args)

if __name__=="__main__":
    main()
