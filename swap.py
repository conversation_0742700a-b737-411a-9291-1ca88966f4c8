import os
import glob
import json
import argparse
from pathlib import Path
from collections import defaultdict
import cv2
import numpy as np
from tqdm import tqdm

from utils.utils import CameraCalibrator
from utils.face_templates import face_mesh_template_3D_MEDIAPIPE_μm
from utils.face_templates import head_pose_indices_deltax


HD = (1280, 720)
FHD = (1920, 1080)


def transform_3d_points_between_cams(pts_3d_src, T_src, T_dst):
    """
    pts_3d_src : (N, 3) keypoints in source camera coordinates
    T_src      : (4, 4) extrinsic of source camera (world←src)
    T_dst      : (4, 4) extrinsic of destination camera (world←dst)
    """
    # make homogeneous
    pts_h = np.hstack([pts_3d_src, np.ones((len(pts_3d_src), 1))])  # (N,4)

    # transform
    T_src_to_dst = np.linalg.inv(T_dst) @ T_src
    pts_3d_dst_h = (T_src_to_dst @ pts_h.T).T  # (N,4)

    return pts_3d_dst_h[:, :3]  # (N,3)


def get_face_landmarks_in_ccs_from_solvePnP(model_points, image_points, K, D):
    """ Solve PnP to get rotation and translation vectors """
    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        model_points,
        image_points,
        K,
        D,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )   # Initial fit
    if inliers is not None:
        m = model_points[inliers[:, 0]]
        i = image_points[inliers[:, 0]]
    else:
        m = model_points
        i = image_points
    
    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(
            m,
            i,
            K,
            D,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )   # Second fit for higher accuracy
    
    return rvec, tvec # Shape: (3, N)


def transform(sample_group_0, sample_group_2, camera_params):
    # Get image
    original_0 = cv2.imread(sample_group_0['image_path'])
    original_2 = cv2.imread(sample_group_2['image_path'])
    
    # Get keypoints
    keypoints_0 = sample_group_0['keypoints']
    keypoints_2 = sample_group_2['keypoints']
        
    # Undistort
    params = camera_params["camera_0"]
    calibrator_0 = CameraCalibrator(params["K"], params["D"])
    original_0 = calibrator_0.undistort_image(original_0, 0.0)[0]
    keypoints_0 = calibrator_0.undistort_point(keypoints_0)
    K_new_0, D_new_0 = calibrator_0.camera_matrix_undistorted, calibrator_0.dist_coeffs_undistorted
    
    params = camera_params["camera_1"]
    calibrator_2 = CameraCalibrator(params["K"], params["D"])
    original_2 = calibrator_2.undistort_image(original_2, 0.0)[0]
    keypoints_2 = calibrator_2.undistort_point(keypoints_2)
    K_new_2, D_new_2 = calibrator_2.camera_matrix_undistorted, calibrator_2.dist_coeffs_undistorted
    
    # Get image points, model points
    ids = np.where(keypoints_2[:, 2] == 2)[0]   # indices where condition is true
    image_points = keypoints_2[ids, :2].astype(np.float32)         # the corresponding (x,y)
    
    model_points = face_mesh_template_3D_MEDIAPIPE_μm[head_pose_indices_deltax, :3]
    model_points = model_points[ids, :3]
    
    # Solve PnP
    rvec, tvec = get_face_landmarks_in_ccs_from_solvePnP(
        model_points, image_points, K_new_2, D_new_2
    )

    # R = cv2.Rodrigues(rvec)[0]
    # draw_coordinate_axes_px(original_2, tvec, R, K_new_2, axis_length=80, thickness=3)
    cv2.drawFrameAxes(original_2, K_new_2, D_new_2, rvec, tvec, 50, thickness=3)
    
    proj_points, _ = cv2.projectPoints(
        model_points,
        rvec,
        tvec,
        K_new_2,
        D_new_2,
    )
    proj_points = proj_points.reshape(-1, 2)
    
    # Draw projected points
    for p in proj_points:
        cv2.circle(original_2, (int(p[0]), int(p[1])), 3, (0, 255, 255), -1)
        
    rvec_obj_in_dst_cam, tvec_obj_in_dst_cam = calibrator_0.transform_object_pose_between_cameras(
        rvec_obj_in_src_cam=rvec,
        tvec_obj_in_src_cam=tvec,
        src_cam_extrinsic=camera_params["camera_1"]["pose"],
        dst_cam_extrinsic=camera_params["camera_0"]["pose"],
        verbose=False,
    )
    cv2.drawFrameAxes(original_0, K_new_0, D_new_0, rvec_obj_in_dst_cam, tvec_obj_in_dst_cam, 200, thickness=3)

    proj_points, _ = cv2.projectPoints(
        model_points,
        rvec_obj_in_dst_cam,
        tvec_obj_in_dst_cam,
        K_new_0,
        D_new_0,
    )
    proj_points = proj_points.reshape(-1, 2)
    
    # Draw projected points
    for p in proj_points:
        cv2.circle(original_0, (int(p[0]), int(p[1])), 3, (0, 255, 255), -1)
    
    return original_2, keypoints_2, original_0, proj_points


def get_bbox(original, keypoints, pad=200):
    # Get center of visible koordinate
    w, h = original.shape[1], original.shape[0]
    # visible_keypoints = keypoints[keypoints[:, 2] == 2]
    visible_keypoints = keypoints
    cx = round(np.mean(visible_keypoints[:, 0]))
    cy = round(np.mean(visible_keypoints[:, 1]))

    x0 = max(cx-pad, 0)
    y0 = max(cy-pad, 0)
    x1 = min(cx+pad, w)
    y1 = min(cy+pad, h)
    return x0, x1, y0, y1


def project_point(point3d, K):
    X, Y, Z = point3d
    u = (K[0, 0] * X + K[0, 2] * Z) / Z
    v = (K[1, 1] * Y + K[1, 2] * Z) / Z
    return np.array([u, v], dtype=float)



def unproject_pixels(uvs, K, depth=1.0):
    """
    Back-project multiple 2D pixels into 3D camera coordinates.

    Parameters
    ----------
    uvs : (N, 2) ndarray
        Pixel coordinates [[u, v], ...].
    K : (3,3) ndarray
        Camera intrinsic matrix.
    depth : float or (N,) array_like
        Depth value(s). If scalar, applied to all points.
        If array of length N, uses per-point depth.

    Returns
    -------
    points3d : (N, 3) ndarray
        3D points in camera coordinates.
    """
    fx, fy = K[0, 0], K[1, 1]
    cx, cy = K[0, 2], K[1, 2]

    uvs = np.asarray(uvs, dtype=float)
    depths = np.full(len(uvs), depth) if np.isscalar(depth) else np.asarray(depth, dtype=float)

    X = (uvs[:, 0] - cx) * depths / fx
    Y = (uvs[:, 1] - cy) * depths / fy
    Z = depths

    return np.stack([X, Y, Z], axis=1)


def draw_coordinate_axes_px(img, face_center_3d, R, K, axis_length=80, thickness=3):
    """
    Draws a 3D coordinate system (axes) on the face in the image.
    :param img: Image to draw on
    :param face_center_3d: Origin point in 3D (camera space)
    :param R: 3x3 rotation matrix of the head
    :param K: Camera intrinsic matrix
    :param axis_length: Length of axes to draw (in mm)
    """
    # Axes in 3D camera space
    axes = np.eye(3) * axis_length  # X, Y, Z axes

    # Rotate axes using head rotation matrix
    rotated_axes = R @ axes  # shape (3, 3)

    # Get endpoints in 3D
    endpoints_3d = face_center_3d.reshape(3, 1) + rotated_axes

    # Project to 2D
    origin_2d = project_point(face_center_3d, K)
    x_2d = project_point(endpoints_3d[:, 0], K)
    y_2d = project_point(endpoints_3d[:, 1], K)
    z_2d = project_point(endpoints_3d[:, 2], K)

    # Convert to int tuples
    origin_2d = tuple(map(int, origin_2d))
    x_2d = tuple(map(int, x_2d))
    y_2d = tuple(map(int, y_2d))
    z_2d = tuple(map(int, z_2d))

    # Draw each axis: X (Red), Y (Green), Z (White)
    cv2.line(img, origin_2d, z_2d, (255, 255, 255), thickness) # Z: directed inside head
    cv2.line(img, origin_2d, x_2d, (0, 0, 0), thickness)
    cv2.line(img, origin_2d, y_2d, (0, 0, 0), thickness)

    return img


def load_camera_params():
    # Load camera poses
    cam_poses = cv2.FileStorage("camera_tv_params/calibrated_cameras_data_v2.yml", cv2.FILE_STORAGE_READ)
    num_cameras = int(cam_poses.getNode("nb_camera").real())

    # Display cameras
    camera_params = {}
    for cam_idx in range(num_cameras):
        # Get camera pose
        name = f"camera_{cam_idx}"
        K = cam_poses.getNode(name).getNode("camera_matrix").mat()
        D = cam_poses.getNode(name).getNode("distortion_vector").mat()
        cam_pose = cam_poses.getNode(name).getNode("camera_pose_matrix").mat()
        
        # Get rotation and translation
        cam_R = cam_pose[0:3, 0:3]
        cam_tvec = np.asarray([cam_pose[0:3, 3]]).T
        
        # Store info
        camera_params[name] = {"K": K, "D": D, "pose": cam_pose, "R": cam_R, "tvec": cam_tvec}
    return camera_params


def match_images_and_keypoints(image_paths, jsons_paths):
    
    scene_groups = defaultdict(lambda: defaultdict(lambda: {'image_path': None, 'keypoints': None}))

    for img_path in image_paths:
        base_name = os.path.basename(img_path)
        cam_name = img_path.split("/")[-2]
        scene_groups[base_name][cam_name]['image_path'] = img_path
    
    # Load and organize keypoints for each image
    for json_path in jsons_paths:
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        cam_name = json_path.split("/")[-2]
        # Create mappings
        image_id_to_filename = {img["id"]: img["file_name"] for img in data["images"]}
        image_id_to_keypoints = {ann["image_id"]: ann["keypoints"] for ann in data["annotations"]}

        # Add keypoints to corresponding image groups
        for img_id, filename in image_id_to_filename.items():
            if img_id in image_id_to_keypoints:
                base_name = os.path.basename(filename)
                
                if base_name in scene_groups:
                    keypoints = np.array(image_id_to_keypoints[img_id]).reshape(-1, 3)
                    keypoints = np.round(keypoints).astype(np.int32)
                    scene_groups[base_name][cam_name]['keypoints'] = keypoints
    
    return scene_groups


def display_images(args):
    freeze = args.freeze
    pad = 200
    size = 600
    size_x, size_y = 400, 250
    point_size = 2
    
    winname_0 = "Camera-2-All Points"
    winname_1 = "Camera-2-Only Visible Points"
    winname_2 = "Camera-0-Projected Points"
    
    if args.show_frame:
        for idx, winname in enumerate([winname_0, winname_1, winname_2]):
            cv2.namedWindow(winname, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(winname, width=size, height=size)
            cv2.moveWindow(winname, x=(size_x+pad) * idx, y=size_y)
    
    # Load camera params
    camera_params = load_camera_params()
    
    # All images in the folder
    image_paths = sorted(
        glob.glob(os.path.join(args.images, "**", "*.png"), recursive=True)
    )

    # All jsons in the folder
    jsons_paths = sorted(
        glob.glob(os.path.join(args.jsons, "**", "*.json"), recursive=True)
    )

    # Leave only specific class filter
    image_paths = [name for name in image_paths if args.filter in name]

    # Leave only specific class filter
    jsons_paths = [name for name in jsons_paths if args.filter in name]

    # Match images and keypoints
    scene_groups = match_images_and_keypoints(image_paths, jsons_paths)
    
    # Iterate through the images
    for image_name, camera_groups in tqdm(scene_groups.items()):
        # for cam_name, sample_group in camera_groups.items():
        #     image_path = sample_group['image_path']
        #     keypoints = sample_group['keypoints']
        
        sample_group_0 = scene_groups[image_name]['cam_0']
        sample_group_2 = scene_groups[image_name]['cam_2']
        sample_group_4 = scene_groups[image_name]['cam_4']
        sample_group_6 = scene_groups[image_name]['cam_6']
        sample_group_rs73 = scene_groups[image_name]['rs_146222252273']
        sample_group_rs97 = scene_groups[image_name]['rs_146222251797']
        
        original_2, keypoints_2, original_0, proj_points = transform(sample_group_0, sample_group_2, camera_params)
        original_2_visible = original_2.copy()

        for x, y, z in keypoints_2:
            if z == 1:      # invisible
                cv2.circle(original_2,          (x, y), point_size, (0, 0, 255), -1, cv2.LINE_AA)
            elif z == 2:    # visible
                cv2.circle(original_2_visible,  (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)
                cv2.circle(original_2,          (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)

        # Get face
        x0, x1, y0, y1 = get_bbox(original_2, keypoints_2, pad)
        original_2_cropped = original_2[y0:y1, x0:x1]
        # original_2_cropped = original_2
        
        original_2_visible_cropped = original_2_visible[y0:y1, x0:x1]
        # original_2_visible_cropped = original_2_visible
        
        x0, x1, y0, y1 = get_bbox(original_0, proj_points, pad)
        original_0_cropeed = original_0[y0:y1, x0:x1]
        # original_0_cropeed = original_0

        # Show results
        if args.show_frame:
            cv2.imshow(winname_0, original_2_cropped)
            cv2.imshow(winname_1, original_2_visible_cropped)
            cv2.imshow(winname_2, original_0_cropeed)
            
            # Get a key
            key = cv2.waitKey(freeze) & 0xFF

            if key == ord('q'):     # Press 'q' to quit
                break
            elif key == ord('f'):   # Toggle freeze mode
                freeze = 0 if freeze else 1

    if args.show_frame:
        cv2.destroyAllWindows()


def main(root='data'):
    parser = argparse.ArgumentParser()
    parser.add_argument('-j', '--jsons', default=f'{root}/jsons', type=str, help="json file path")
    parser.add_argument('-i', '--images', default=f'{root}/images', type=str, help="image folder path")
    parser.add_argument('-f', '--filter', default='', type=str, help="Search by filter: '2024-09-04'")
    parser.add_argument('-o', '--output-dir', default=f'{root}/visualized_results', type=str, help="Output directory")
    parser.add_argument('-sf', '--show-frame', action='store_true', help="Show frame for verification")
    parser.add_argument('-s', '--save', action='store_true', help="Show 'src' and 'dst' paths for verification")
    parser.add_argument('--freeze', default=0, type=int, help="Freeze mode")
    args = parser.parse_args()
    
    display_images(args)

if __name__=="__main__":
    main()
