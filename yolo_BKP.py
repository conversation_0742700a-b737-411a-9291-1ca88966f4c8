import os
import glob
import argparse
from pathlib import Path

import cv2
import numpy as np
from tqdm import tqdm
from ultralytics import YOLO

        
HD = (1280, 720)
FHD = (1920, 1080)


def display_images(args):
    freeze = args.freeze
    model = YOLO(args.model, verbose=False)  # load an official model

    winname = "YOLO Inference"
    cv2.namedWindow(winname, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(winname, width=HD[0], height=HD[1])
    cv2.moveWindow(winname, x=FHD[0]//2-HD[0]//2, y=FHD[1]//4-HD[1]//4)
    
    # All images in the folder
    image_paths = sorted(
        glob.glob(os.path.join(args.images, "**", "*.png"), recursive=True)
    )

    # Leave only specific class filter
    image_paths = [name for name in image_paths if args.filter in name]
    
    # Iterate through the images
    for image_path in tqdm(image_paths):
        # Predict with the model
        result = model.predict(image_path, save=False, imgsz=640, conf=0.5, verbose=False)[0]

        # Visualize the results on the frame
        annotated_frame = result.plot()

        # Display the annotated frame
        cv2.imshow(winname, annotated_frame)

        if args.save:
            # Get keypoints from the result
            keypoints = result.keypoints.data.cpu().numpy()
            
            # Create output numpy path (same name as image but with .npy extension)
            npy_path = str(Path(image_path).with_suffix('.npy'))
            
            # Save keypoints as numpy array
            if len(keypoints) > 0:
                np.save(npy_path, keypoints)

        # Get a key
        key = cv2.waitKey(freeze) & 0xFF

        if key == ord('q'):     # Press 'q' to quit
            break
        elif key == ord('f'):   # Toggle freeze mode
            freeze = 0 if freeze else 1
    
    cv2.destroyAllWindows()


def main(root='data'):
    parser = argparse.ArgumentParser()
    parser.add_argument('-i', '--images', default=f'{root}/images', type=str, help="image folder path")
    parser.add_argument('-f', '--filter', default="", type=str, help="Search by filter: '2024-06-26'")
    parser.add_argument('-m', '--model', default="weights/yolo11x-pose.pt", type=str, help="Model name")
    parser.add_argument('-s', '--save', action='store_true', help="Save processed images")
    parser.add_argument('--freeze', default=0, type=int, help="Freeze mode")
    args = parser.parse_args()

    display_images(args)


if __name__=="__main__":
    main()
