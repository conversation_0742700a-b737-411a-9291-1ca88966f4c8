# === Standard Libraries ===
import logging
from typing import *
import xml.etree.ElementTree as ET

# === Third-Party Libraries ===
import cv2
import numpy as np


class CameraCalibrator:
    """Class for calibrating cameras using Charuco boards."""

    def __init__(self, camera_matrix = None, dist_coeffs = None, is_fisheye = True):
        """Initialize the CameraCalibrator.

        Args:
            camera_matrix: Camera intrinsic matrix
            dist_coeffs: Distortion coefficients
            is_fisheye: Whether to use fisheye camera model (default: True)
        """
        # Calibration results
        self.camera_matrix = camera_matrix
        self.dist_coeffs = dist_coeffs
        self.is_fisheye = is_fisheye

        self.camera_matrix_undistorted = None
        if is_fisheye:
            self.dist_coeffs_undistorted = np.zeros((1, 4))
        else:
            self.dist_coeffs_undistorted = np.zeros((1, 5))

    def estimate_pose_solvePnP(self,
                               imgpoints: np.ndarray,
                               objpoints: np.ndarray
                               ) -> Tuple[bool, Optional[np.ndarray], Optional[np.ndarray]]:
        """Estimate pose of the Charuco board.

        Args:
            charuco_corners: Detected Charuco corners
            charuco_ids: IDs of detected Charuco corners

        Returns:
            Tuple containing:
                - success: True if pose estimation was successful
                - rvec: Rotation vector (None if estimation failed)
                - tvec: Translation vector (None if estimation failed)
        """
        try:
            success, rvec, tvec = cv2.solvePnP(
                objpoints,
                imgpoints,
                self.camera_matrix_undistorted,
                self.dist_coeffs_undistorted,
                None,
                None,
                flags=cv2.SOLVEPNP_ITERATIVE
            )

            if success:
                rvec = np.array(rvec, dtype=np.float64)
                tvec = np.array(tvec, dtype=np.float64)
                return True, rvec, tvec
            else:
                return False, None, None
        except Exception as e:
            logging.error(f"❌ Error estimating pose: {str(e)}")
            return False, None, None
    
    def draw_pose_cv2(self,
                  frame: np.ndarray,
                  rvec: np.ndarray,
                  tvec: np.ndarray,
                  axis_length: float = 0.1) -> None:
        """Draw 3D axes on the frame.

        Args:
            frame: Input frame to draw on
            rvec: Rotation vector
            tvec: Translation vector
            axis_length: Length of axes to draw
        """
        cv2.drawFrameAxes(frame, self.camera_matrix_undistorted, self.dist_coeffs_undistorted, rvec, tvec, axis_length)

    def project_points(self,
                       frame: np.ndarray,
                       rvec: np.ndarray,
                       tvec: np.ndarray,
                       objpoints: np.ndarray,
                       color: Tuple[int, int, int] = (0, 0, 255),
                       radius: int = 5
                       ) -> None:
        """Project 3D points to image plane.

        Args:
            frame: Input frame to draw on
            rvec: Rotation vector
            tvec: Translation vector
            objpoints_type: Type of object points to project
            color: Color to draw points (BGR)
            radius: Radius of points to draw
        """
        proj_points, _ = cv2.projectPoints(
            objpoints,
            rvec,
            tvec,
            self.camera_matrix_undistorted,
            self.dist_coeffs_undistorted,
        )
        proj_points = proj_points.reshape(-1, 2)
        
        # Draw projected points
        for p in proj_points:
            cv2.circle(frame, (int(p[0]), int(p[1])), radius, color, -1)
            
    def undistort_point(
        self,
        keypoints: np.ndarray,
        R: Optional[np.ndarray] = None
    ) -> Optional[np.ndarray]:
        """Undistort keypoints to rectified pixel coordinates.

        Args:
            keypoints: Array of keypoints with shape (N, 3) where columns are [x, y, visibility]
            R: Optional 3x3 rectification rotation. Defaults to identity.

        Returns:
            Rectified keypoints array, or None on failure.
        """
        try:
            if (self.camera_matrix is None) or (self.dist_coeffs is None):
                logging.error("❌ No calibration data available")
                return None

            if R is None:
                R = np.eye(3, dtype=np.float64)

            pts = keypoints[:, :2]

            # Shape must be (N, 1, 2), dtype float64
            distorted_pts = np.array(pts).reshape(-1, 1, 2).astype(np.float64)

            if self.is_fisheye:
                # Fisheye undistortion
                rectified_pts = cv2.fisheye.undistortPoints(
                    distorted=distorted_pts,
                    K=self.camera_matrix,
                    D=self.dist_coeffs,
                    R=R,
                    P=self.camera_matrix_undistorted
                )
            else:
                # Pinhole camera undistortion
                rectified_pts = cv2.undistortPoints(
                    distorted_pts,
                    self.camera_matrix,
                    self.dist_coeffs,
                    R=R,
                    P=self.camera_matrix_undistorted
                )

            keypoints[:, :2] = rectified_pts.reshape(-1, 2)

            return keypoints

        except Exception as e:
            logging.error(f"❌ Error undistorting point: {str(e)}")
            return None

    def undistort_image(self, image: np.ndarray, balance: float = 1.0) -> np.ndarray:
        """Undistort an image using calibration parameters.

        Args:
            image: Input image
            balance: Balance value for undistortion (0.0 = crop, 1.0 = stretch)

        Returns:
            Undistorted image and new camera matrix
        """
        if (self.camera_matrix is None) or (self.dist_coeffs is None):
            logging.error(f"❌ No calibration data available")
            return image, None

        h, w = image.shape[:2]
        img_size = (w, h)

        if self.is_fisheye:
            # Fisheye undistortion
            R = np.eye(3)
            m1type = cv2.CV_16SC2

            # Get optimal new camera matrix for fisheye
            new_camera_matrix = cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(
                K=self.camera_matrix,
                D=self.dist_coeffs,
                image_size=img_size,
                R=R,
                balance=balance
            )

            # Create undistortion maps
            map1, map2 = cv2.fisheye.initUndistortRectifyMap(
                K=self.camera_matrix,
                D=self.dist_coeffs,
                R=R,
                P=new_camera_matrix,
                size=img_size,
                m1type=m1type
            )

            # Apply undistortion
            undistorted = cv2.remap(
                src=image,
                map1=map1,
                map2=map2,
                interpolation=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT
            )
        else:
            # Pinhole camera undistortion
            new_camera_matrix, roi = cv2.getOptimalNewCameraMatrix(
                self.camera_matrix,
                self.dist_coeffs,
                img_size,
                balance
            )

            # Undistort the image
            undistorted = cv2.undistort(
                image,
                self.camera_matrix,
                self.dist_coeffs,
                None,
                new_camera_matrix
            )

        self.camera_matrix_undistorted = new_camera_matrix

        return undistorted, new_camera_matrix
    
    def transform_object_pose_between_cameras(
        self,
        rvec_obj_in_src_cam: np.ndarray,
        tvec_obj_in_src_cam: np.ndarray,
        src_cam_extrinsic: np.ndarray,
        dst_cam_extrinsic: np.ndarray,
        verbose: bool = False
    ) -> tuple[np.ndarray, np.ndarray]:
        """
        Transform a 3D object's pose from the source camera's coordinate system 
        to the target (destination) camera's coordinate system.

        Parameters:
        ----------
        rvec_obj_in_src_cam : (3, 1) rotation vector of the object in source camera coordinates
        tvec_obj_in_src_cam : (3, 1) translation vector of the object in source camera coordinates
        src_cam_extrinsic : (4, 4) extrinsic matrix of the source camera in world coordinates
        dst_cam_extrinsic : (4, 4) extrinsic matrix of the target camera in world coordinates
        verbose : bool
            If True, prints intermediate transformation matrices

        Returns:
        -------
        rvec_obj_in_dst_cam : (3, 1) rotation vector of the object in destination camera coordinates
        tvec_obj_in_dst_cam : (3, 1) translation vector of the object in destination camera coordinates
        """

        # Convert object rotation vector to rotation matrix
        R_obj_in_src_cam = cv2.Rodrigues(rvec_obj_in_src_cam)[0]

        # Construct the 4x4 pose matrix (homogeneous) of the object in source camera frame
        T_obj_in_src_cam = np.eye(4)
        T_obj_in_src_cam[:3, :3] = R_obj_in_src_cam
        T_obj_in_src_cam[:3, 3] = tvec_obj_in_src_cam.flatten()

        # Compute the transformation from source camera to destination camera
        T_from_src_to_dst = np.linalg.inv(dst_cam_extrinsic) @ src_cam_extrinsic

        # Apply the transformation to move the object's pose into destination camera coordinates
        T_obj_in_dst_cam = T_from_src_to_dst @ T_obj_in_src_cam

        # Extract rotation and translation from the transformed pose
        R_obj_in_dst_cam = T_obj_in_dst_cam[:3, :3]
        rvec_obj_in_dst_cam = cv2.Rodrigues(R_obj_in_dst_cam)[0]
        tvec_obj_in_dst_cam = T_obj_in_dst_cam[:3, 3].reshape(3, 1)

        if verbose:
            print("T_src_to_dst:\n", T_from_src_to_dst)
            print("T_obj_in_dst_cam:\n", T_obj_in_dst_cam)
            print("rvec_obj_in_dst_cam:\n", rvec_obj_in_dst_cam)
            print("tvec_obj_in_dst_cam:\n", tvec_obj_in_dst_cam)
            
        return rvec_obj_in_dst_cam, tvec_obj_in_dst_cam
